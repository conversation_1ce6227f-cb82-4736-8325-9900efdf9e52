<template>
  <div class="project-idea-container">

     <!-- v-loading="isLoading" -->
    <div class="design-section story-design-section animation-delay-1">
      <div :class="isEditing ? 'info-column' : 'info-grid'">
        <div class="section-header" style="grid-column: 1 / -1;">
          <el-icon>
            <EditPen />
          </el-icon>
          <h3 class="section-title">创意配置</h3>
          <div class="section-actions" v-if="!isEditing">
            <el-tooltip content="编辑" placement="top" :effect="'light'" v-if="false">
              <div class="edit-btn" @click="startEditing">
                <el-icon>
                  <Edit />
                </el-icon>
              </div>
            </el-tooltip>
          </div>
          <div class="section-actions" v-else>
            <el-tooltip content="取消" placement="top" :effect="'light'">
              <div class="cancel-btn" @click="cancelEditing">
                <el-icon>
                  <Close />
                </el-icon>
              </div>
            </el-tooltip>
            <el-tooltip content="保存" placement="top" :effect="'light'">
              <div class="save-btn" @click="saveEditing">
                <el-icon>
                  <CircleCheck />
                </el-icon>
              </div>
            </el-tooltip>
          </div>
        </div>

        <!-- 音效 -->
        <div class="info-item">
          <div class="info-label">
            <el-icon>
              <Headset />
            </el-icon>
            <span>旁白</span>
          </div>
          <div v-if="!isEditing" class="info-value">
            <span class="voice-name" @click.stop="handleVoiceNameClick">{{ projectData.soundName || '未设置' }}</span>
            <span class="voice-id-note" v-if="projectData.soundId">(ID: {{ projectData.soundId }})</span>
            <!-- 添加试听按钮 -->
            <div v-if="projectData.soundId" class="voice-play-button"
              :class="{ 'playing': isPlaying(projectData.soundId) }" @click.stop="playSound(projectData.soundId)">
              <el-icon>
                <VideoPause v-if="isPlaying(projectData.soundId)" />
                <VideoPlay v-else />
              </el-icon>
              <span class="wave-animation" v-if="isPlaying(projectData.soundId)">
                <span class="wave-bar"></span>
                <span class="wave-bar"></span>
                <span class="wave-bar"></span>
                <span class="wave-bar"></span>
              </span>
            </div>
          </div>
          <div v-else class="item-value-edit voice-selector-container">
            <VoiceSelector :voices="voices" :selectedVoice="tempData.soundId ? Number(tempData.soundId) : null" :isLoading="isLoadingVoices"
              @update:selectedVoice="handleVoiceSelect" />
          </div>
        </div>

        
        <!-- 图片风格 -->
        <div class="info-item">
          <div class="info-label">
            <el-icon>
              <PictureRounded />
            </el-icon>
            <span>图片风格</span>
          </div>
          <!-- v-if="!isEditing"  -->
          <div class="info-value">
            <span>{{ projectData.imageStyleName || '未设置' }}</span>
            <span class="style-prompt-text" v-if="projectData.imageStyleId">(ID: {{ projectData.imageStyleId }})</span>
          </div>
          <!-- <div v-else class="item-value-edit style-selector-container">
            <StyleSelector :styles="styles" v-model:selectedStyleId="selectedStyleId" :isLoading="isLoadingStyles"
              @style-select="handleStyleSelect" />
          </div> -->
        </div>

        <!-- 图片尺寸 -->
        <div class="info-item">
          <div class="info-label">
            <el-icon>
              <Reading />
            </el-icon>
            <span>图片尺寸</span>
          </div>
          <!-- v-if="!isEditing"  -->
          <div class="info-value">{{ projectData.imageSize || '未设置' }}</div>
          <!-- <div v-else class="item-value-edit">
            <RatioSelector v-model="tempData.imageSize" @ratio-change="handleRatioSelect" />
          </div> -->
        </div>

        <!-- 创作参考图 -->
        <div class="info-item prompt-item" v-if="projectData.imageList && projectData.imageList.length > 0">
          <div class="info-label">
            <el-icon>
              <PictureRounded />
            </el-icon>
            <span>创作参考图</span>
          </div>
          <div class="reference-images-container">
            <div class="reference-images-grid">
              <div
                v-for="(imageUrl, index) in projectData.imageList"
                :key="index"
                class="reference-image-item"
                @click="handleReferenceImageClick(imageUrl, index)"
              >
                <img
                  :src="imageUrl + '?x-oss-process=image/resize,w_200'"
                  :alt="`参考图 ${index + 1}`"
                  class="reference-image"
                  @error="handleReferenceImageError($event, index)"
                />
                <div class="image-overlay">
                  <el-icon class="preview-icon">
                    <View />
                  </el-icon>
                </div>
              </div>
            </div>
          </div>
        </div>

        <!-- 提示词 -->
        <div class="info-item prompt-item">
          <div class="info-label">
            <el-icon>
              <EditPen />
            </el-icon>
            <span>提示词</span>
          </div>
          <div class="theme-value-container">
            <div
              class="theme-value"
              :class="{ 'theme-value-limited': isPromptLimited }"
              ref="promptContentRef"
            >
              {{ projectData.prompt || '未设置' }}
            </div>
            <div
              class="expand-button"
              @click="openPromptModal"
            >
              <span>显示更多</span>
              <el-icon>
                <ArrowDown />
              </el-icon>
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- <div class="design-section story-design-section animation-delay-1" v-loading="isLoading">
      <div class="info-grid">
      </div>
    </div> -->

    <!-- 设计项内容部分 -->
    <div class="design-section animation-delay-1" v-loading="isLoading" v-if="hasDesignItems">
      <div class="section-header">
        <el-icon>
          <Document />
        </el-icon>
        <h3 class="section-title">设计内容</h3>
      </div>
      <div class="info-grid">
        <div v-for="(item, index) in projectData.designList" :key="index" class="info-item prompt-item">
          <div class="info-label">
            <el-icon>
              <Document />
            </el-icon>
            <span>{{ item.key }}</span>
            <el-tooltip content="复制内容" placement="top" :effect="'light'" v-if="item.value">
              <div class="copy-btn" @click="copyDesignItemContent(item.value)">
                <el-icon>
                  <CopyDocument />
                </el-icon>
              </div>
            </el-tooltip>
          </div>
          <div class="script-content markdown-content" v-html="renderMarkdown(item.value || '未设置')"></div>
        </div>
      </div>
    </div>

  </div>

  <!-- 提示词详情弹框 -->
  <div class="prompt-modal" v-if="promptModalVisible">
    <div class="prompt-backdrop" @click="closePromptModal"></div>
    <div class="prompt-modal-container">
      <div class="prompt-modal-header">
        <h3>提示词详情</h3>
        <el-icon class="close-icon" @click="closePromptModal">
          <Close />
        </el-icon>
      </div>
      <div class="prompt-modal-body">
        <div class="prompt-modal-content">
          <div class="prompt-label">
            <el-icon><EditPen /></el-icon>
            <span>完整提示词</span>
            <el-tooltip content="复制内容" placement="top" :effect="'light'" v-if="projectData.prompt">
              <div class="copy-btn" @click="copyPromptContent(projectData.prompt)">
                <el-icon><CopyDocument /></el-icon>
              </div>
            </el-tooltip>
          </div>
          <div class="prompt-full-content">{{ projectData.prompt || '未设置' }}</div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref, onMounted, watch, onBeforeUnmount, computed } from 'vue'
import { Reading, PictureRounded, EditPen, Headset, Edit, Close, CircleCheck, VideoPlay, VideoPause, Document, CopyDocument, View, ArrowDown } from '@element-plus/icons-vue'
import { getConversationDetail, updateConversation, getImageStyleList } from '@/api/auth'
import { ElMessage } from 'element-plus'
import StyleSelector from './selector/StyleSelector.vue'
import VoiceSelector from './selector/VoiceSelector.vue'
import RatioSelector from './selector/RatioSelector.vue'
import markdownit from 'markdown-it'

// 初始化 markdown-it
const md = markdownit({
  html: true,
  breaks: true,
  linkify: true
})

// 渲染Markdown内容
const renderMarkdown = (content) => {
  if (!content) return ''
  return md.render(content)
}

// 定义props
const props = defineProps({
  conversationId: {
    type: String,
    default: ''
  },
  voices: {
    type: Array,
    default: () => []
  },
  isLoadingVoices: {
    type: Boolean,
    default: false
  },
  activeTab: {
    type: String,
    default: ''
  }
})

// 定义emit
const emit = defineEmits(['refresh-project-data', 'update-ratio', 'open-voice-selector'])

// 项目创意数据
const projectData = ref({
  conversationId: "",
  imageSize: null,
  imageStyleId: null,
  imageStyleName: "",
  prompt: "",
  soundId: null,
  soundName: "",
  designList: [],
  imageList: []
})

// 编辑状态
const isEditing = ref(false)
// 临时数据，用于编辑
const tempData = ref({})
// 加载状态
const isLoading = ref(false)

// 图片风格相关
const isLoadingStyles = ref(false)
const styles = ref([])
const selectedStyleId = ref(null)

// 音频播放相关
const currentAudio = ref(null)
const currentPlayingId = ref(null)

// 提示词相关
const promptModalVisible = ref(false)
const promptContentRef = ref(null)
const isPromptLimited = ref(false)
const showPromptExpandButton = ref(false)

// 检查提示词是否需要限制显示
const checkPromptLimit = () => {
  if (!projectData.value.prompt) {
    isPromptLimited.value = false
    showPromptExpandButton.value = false
    return
  }

  // 计算行数（按换行符分割）
  const lines = projectData.value.prompt.split('\n')
  if (lines.length > 5) {
    isPromptLimited.value = true
    showPromptExpandButton.value = true
  } else {
    isPromptLimited.value = false
    showPromptExpandButton.value = false
  }
}

// 打开提示词弹框
const openPromptModal = () => {
  promptModalVisible.value = true
}

// 关闭提示词弹框
const closePromptModal = () => {
  promptModalVisible.value = false
}

// 复制提示词内容
const copyPromptContent = (content) => {
  if (!content) return

  navigator.clipboard.writeText(content)
    .then(() => {
      ElMessage.success('内容已复制到剪贴板')
    })
    .catch(err => {
      console.error('复制失败:', err)
      ElMessage.error('复制失败，请手动选择并复制')
    })
}

// 判断是否正在播放特定音效
const isPlaying = (soundId) => {
  return currentPlayingId.value === soundId && currentAudio.value && !currentAudio.value.paused
}

// 播放音效示例
const playSound = (soundId) => {
  // 如果正在播放当前音效，则暂停
  if (isPlaying(soundId)) {
    currentAudio.value.pause()
    currentAudio.value = null
    currentPlayingId.value = null
    return
  }

  // 如果有其他正在播放的音频，先停止
  if (currentAudio.value) {
    currentAudio.value.pause()
    currentAudio.value = null
    currentPlayingId.value = null
  }

  // 查找选中的音效
  const sound = props.voices.find(v => v.id === soundId)

  // 创建新的音频元素并播放
  if (sound && sound.audioUrl) {
    const audioUrl = sound.audioUrl
    const audio = new Audio(audioUrl)

    // 设置事件监听
    audio.addEventListener('play', () => {
      currentPlayingId.value = soundId
    })

    audio.addEventListener('ended', () => {
      currentAudio.value = null
      currentPlayingId.value = null
    })

    audio.addEventListener('pause', () => {
      if (currentPlayingId.value === soundId) {
        currentPlayingId.value = null
      }
    })

    audio.addEventListener('error', () => {
      console.error('音频播放错误')
      currentAudio.value = null
      currentPlayingId.value = null
      ElMessage.error('音频播放失败，请重试')
    })

    // 播放
    audio.play().catch(error => {
      console.error('播放失败:', error)
      currentAudio.value = null
      currentPlayingId.value = null
      ElMessage.error('音频播放失败，请重试')
    })

    currentAudio.value = audio
  } else {
    ElMessage.warning('该音效没有试听音频')
  }
}

// 获取项目创意数据
const fetchProjectData = async () => {
  console.log('fetchProjectData', props.conversationId)
  if (!props.conversationId) return

  try {
    isLoading.value = true
    const response = await getConversationDetail(props.conversationId)
    if (response && response.success) {
      projectData.value = {
        conversationId: response.data.conversationId || "",
        imageSize: response.data.imageSize || "16:9",
        imageStyleId: response.data.imageStyleId || null,
        imageStyleName: response.data.imageStyleName || "",
        prompt: response.data.prompt || "",
        soundId: response.data.soundId || null,
        soundName: response.data.soundName || "",
        designList: response.data.designList || [],
        imageList: response.data.imageList || []
      }
      // 初始化选中的风格ID
      selectedStyleId.value = projectData.value.imageStyleId

      // 通知父组件图片尺寸变化
      emit('update-ratio', projectData.value.imageSize)

      // 检查提示词是否需要限制显示
      checkPromptLimit()
    } else {
      ElMessage.error('获取项目创意数据失败')
    }
  } catch (error) {
    console.error('获取项目创意数据异常:', error)
    ElMessage.error('获取项目创意数据异常')
  } finally {
    isLoading.value = false
  }
}

// 获取当前图片尺寸
const getImageSize = () => {
  return projectData.value.imageSize || '16:9'
}

// 暴露给父组件的刷新方法
const refreshProjectData = () => {
  console.log('ProjectIdea - refreshProjectData 被调用')
  fetchProjectData()
}

// 将方法暴露给父组件
defineExpose({
  refreshProjectData,
  getImageSize
})

// 获取画面风格列表
const fetchImageStyles = async () => {
  try {
    isLoadingStyles.value = true
    const response = await getImageStyleList()
    if (response.success) {
      styles.value = response.data.map(style => ({
        id: style.id,
        name: style.styleName,
        category: style.styleCode || '',
        preview: style.styleUrl || '',
        styleCategory: style.styleCategory || '',
        prompt: style.prompt || ''
      }))

      // 如果已有设置的风格ID，设置选中状态
      if (projectData.value.imageStyleId) {
        selectedStyleId.value = projectData.value.imageStyleId
      }
    } else {
      console.error('获取画面风格列表失败:', response.data?.errMessage)
    }
  } catch (error) {
    console.error('获取画面风格列表异常:', error)
  } finally {
    isLoadingStyles.value = false
  }
}

// 选择风格
const handleStyleSelect = (styleId) => {
  selectedStyleId.value = styleId
  const selectedStyle = styles.value.find(style => style.id === styleId)
  if (selectedStyle) {
    tempData.value.imageStyleId = styleId
    tempData.value.imageStyleName = selectedStyle.name
  }
}

// 选择音色
const handleVoiceSelect = (voiceId) => {
  tempData.value.soundId = voiceId
  const selectedVoice = props.voices.find(voice => voice.id === voiceId)
  if (selectedVoice) {
    tempData.value.soundName = selectedVoice.name
  }
}

// 选择图片尺寸
const handleRatioSelect = (ratio) => {
  tempData.value.imageSize = ratio
}

// 开始编辑
const startEditing = () => {
  isEditing.value = true
  // 复制当前数据到临时数据
  tempData.value = { ...projectData.value }
  // 设置选中的风格ID
  selectedStyleId.value = tempData.value.imageStyleId
}

// 取消编辑
const cancelEditing = () => {
  isEditing.value = false
  // 重置临时数据
  tempData.value = {}
  // 重置选中的风格ID
  selectedStyleId.value = projectData.value.imageStyleId
}

// 保存编辑
const saveEditing = async () => {
  try {
    isLoading.value = true

    // 构建更新数据
    const updateData = {
      conversationId: props.conversationId,
      imageSize: tempData.value.imageSize,
      imageStyleId: tempData.value.imageStyleId,
      soundId: tempData.value.soundId
    }

    const response = await updateConversation(updateData)
    if (response && response.success) {
      // 更新本地数据
      projectData.value = {
        ...projectData.value,
        imageSize: tempData.value.imageSize,
        imageStyleId: tempData.value.imageStyleId,
        imageStyleName: tempData.value.imageStyleName,
        soundId: tempData.value.soundId,
        soundName: tempData.value.soundName
      }

      // 通知父组件图片尺寸变化
      emit('update-ratio', projectData.value.imageSize)

      ElMessage.success('更新成功')
      isEditing.value = false
    } else {
      ElMessage.error('更新失败: ' + (response.data?.errMessage || '未知错误'))
    }
  } catch (error) {
    console.error('更新项目创意数据异常:', error)
    ElMessage.error('更新项目创意数据异常')
  } finally {
    isLoading.value = false
  }
}

// 获取音色名称
const getVoiceName = (voiceId) => {
  if (!voiceId) return '未设置'
  const voice = props.voices.find(v => v.id == voiceId)
  return voice ? voice.name : voiceId
}

// 处理点击旁白声音名称
const handleVoiceNameClick = () => {
  // 触发事件，让父组件打开音色选择器
  // 传递当前的旁白音色ID，使用特殊的字符ID来标识这是旁白音色
  emit('open-voice-selector', 'narrator', projectData.value.soundId)
}

// 监听conversationId变化
watch(() => props.conversationId, (newVal) => {
  console.log('fetchProjectData watch', props.conversationId)
  if (newVal) {
    fetchProjectData()
  }
}, { immediate: true })

// 监听图片尺寸变化
watch(() => projectData.value.imageSize, (newVal) => {
  if (newVal) {
    emit('update-ratio', newVal)
  }
})

// 监听 activeTab 变化，当切换到相关 tab 时更新数据
watch(() => props.activeTab, (newTab, oldTab) => {
  console.log('ProjectIdea - activeTab 变化:', oldTab, '->', newTab)

  // 当切换到角色生成或分镜生成 tab 时，刷新项目数据
  if (newTab === 'edit') {
    console.log('ProjectIdea - 检测到切换到相关 tab，刷新数据')
    if (props.conversationId) {
      fetchProjectData()
    }
  }
}, { immediate: false })

// 监听提示词变化，检查是否需要限制显示
watch(() => projectData.value.prompt, () => {
  checkPromptLimit()
}, { immediate: true })

// 组件挂载时获取数据
onMounted(() => {
  fetchImageStyles()
  console.log('fetchProjectData setTimeout()', props.conversationId)
  if (props.conversationId) {
    fetchProjectData()
  }
})

// 组件卸载前清理
onBeforeUnmount(() => {
  // 停止正在播放的音频
  if (currentAudio.value) {
    currentAudio.value.pause()
    currentAudio.value = null
    currentPlayingId.value = null
  }
})

// 设计项相关
const hasDesignItems = computed(() => {
  return false
  // return projectData.value.designList && projectData.value.designList.length > 0
})

// 复制设计项内容
const copyDesignItemContent = (content) => {
  if (!content) return

  navigator.clipboard.writeText(content)
    .then(() => {
      ElMessage.success('内容已复制到剪贴板')
    })
    .catch(err => {
      console.error('复制失败:', err)
      ElMessage.error('复制失败，请手动选择并复制')
    })
}

// 处理参考图点击事件
const handleReferenceImageClick = (imageUrl, index) => {
  // 调用全局图片预览函数
  if (window.openImagePreview) {
    window.openImagePreview(imageUrl, {
      alt: `参考图 ${index + 1}`,
      title: `创作参考图 ${index + 1}`,
      description: '用于创作参考的图片'
    })
  }
}

// 处理参考图加载错误
const handleReferenceImageError = (event, index) => {
  console.error(`参考图 ${index + 1} 加载失败:`, event.target.src)
  // 可以在这里添加错误处理逻辑，比如显示占位图
}
</script>

<style scoped>
.project-idea-container {
  padding: 6px;
  height: 100%;
  overflow-y: auto;
  display: flex;
  flex-direction: column;
  gap: 6px;
}

.section-header {
  display: flex;
  align-items: center;
  gap: 10px;
  margin-bottom: 16px;
  /* padding-bottom: 12px; */
  /* border-bottom: 1px solid #f1f1f1; */
}

.section-header .el-icon {
  color: #6366f1;
  font-size: 22px;
}

.section-title {
  margin: 0;
  font-size: 18px;
  font-weight: 600;
  color: #334155;
}

body.dark .section-title {
  color: #e5e7eb;
  border-bottom-color: #374151;
}

body.dark .section-header {
  border-bottom-color: #2e2e3c;
}

/* 设计部分样式 */
.design-section {
  background-color: rgba(255, 255, 255, 0.484);
  border-radius: 12px;
  padding: 20px;
  box-shadow: 0 0 6px 0 rgba(0, 0, 0, 0.2);
  transition: all 0.3s;
  animation: section-appear 0.1s ease-out both;
  opacity: 1;
}


.story-design-section {
  background: linear-gradient(135deg, #fff 0%, #9396fb52 100%);
}

body.dark .story-design-section {
  background: linear-gradient(135deg, var(--bg-card) 0%, #9396fb52 100%);
}


@keyframes section-appear {
  from {
    opacity: 0;
    transform: translateY(20px);
  }

  to {
    opacity: 1;
    transform: translateY(0);
  }
}

.design-section:hover {
  box-shadow: 0 4px 20px rgba(99, 102, 241, 0.15);
  transform: translateY(-2px);
}

body.dark .design-section {
  background-color: #1616187b;
  border-color: var(--border-color);
  box-shadow: 0 0 10px 0 #3a78ff7e;
}

/* 编辑按钮样式 */
.section-actions {
  display: flex;
  align-items: center;
  gap: 8px;
  margin-left: auto;
}

.edit-btn,
.cancel-btn,
.save-btn {
  width: 28px;
  height: 28px;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  transition: all 0.3s ease;
  box-shadow: 0 2px 6px rgba(0, 0, 0, 0.1);
}

.edit-btn {
  background-color: #4f46e5;
  color: white;
}

.edit-btn:hover {
  transform: scale(1.1);
}

.cancel-btn {
  background-color: #f87171;
  color: white;
}

.cancel-btn:hover {
  transform: scale(1.1);
}

.save-btn {
  background-color: #10b981;
  color: white;
}

.save-btn:hover {
  transform: scale(1.1);
}

/* 信息网格样式 */
.info-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
  gap: 16px;
}

.info-column {
  display: flex;
  flex-direction: column;
  gap: 16px;
}

.info-item {
  display: flex;
  flex-direction: column;
  gap: 8px;
}

.prompt-item {
  grid-column: 1 / -1;
}

.info-label {
  display: flex;
  align-items: center;
  gap: 8px;
  font-size: 14px;
  color: #64748b;
  font-weight: 500;
  text-align: left;
}

body.dark .info-label {
  color: #94a3b8;
}

.info-label .el-icon {
  font-size: 16px;
  color: #6366f1;
}

body.dark .info-label .el-icon {
  color: #818cf8;
}

.section-actions .el-icon {
  color: white;
  font-size: 16px;
}

.info-value {
  font-size: 16px;
  color: #334155;
  font-weight: 500;
  display: flex;
  flex-direction: row;
  align-items: center;
  justify-content: flex-start;
  padding: 10px 14px;
  background-color: #f8fafc55;
  border-radius: 8px;
  border-left: 1px solid #6366f1;
  text-align: left;
}

.theme-value-container {
  display: flex;
  flex-direction: column;
  gap: 6px;
}

.theme-value {
  font-size: 16px;
  color: #334155;
  padding: 10px 14px;
  background-color: #f8fafc55;
  border-radius: 8px;
  border-left: 1px solid #6366f1;
  text-align: left;
  white-space: pre-wrap;
  overflow-wrap: break-word;
}

.theme-value-limited {
  display: -webkit-box;
  -webkit-line-clamp: 3;
  line-clamp: 3;
  -webkit-box-orient: vertical;
  overflow: hidden;
  max-lines: 3;
  text-overflow: ellipsis;
  white-space: nowrap;
}

.expand-button {
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 4px;
  padding: 6px 12px;
  background-color: rgba(99, 102, 241, 0.1);
  border: 1px solid rgba(99, 102, 241, 0.3);
  border-radius: 6px;
  color: #6366f1;
  font-size: 14px;
  cursor: pointer;
  transition: all 0.3s ease;
  align-self: flex-start;
}

.expand-button:hover {
  background-color: rgba(99, 102, 241, 0.2);
  border-color: rgba(99, 102, 241, 0.5);
  transform: translateY(-1px);
}

.expand-button .el-icon {
  font-size: 14px;
}

body.dark .info-value,
body.dark .theme-value {
  color: #e5e7eb;
  background-color: #37415129;
}

body.dark .expand-button {
  background-color: rgba(99, 102, 241, 0.2);
  border-color: rgba(99, 102, 241, 0.4);
  color: #818cf8;
}

body.dark .expand-button:hover {
  background-color: rgba(99, 102, 241, 0.3);
  border-color: rgba(99, 102, 241, 0.6);
}

/* 可点击的音色名称样式 */
.voice-name {
  cursor: pointer;
  color: #409eff;
  transition: all 0.3s;
  padding: 2px 8px;
  border-radius: 4px;
}

.voice-name:hover {
  background-color: rgba(64, 158, 255, 0.1);
  text-decoration: underline;
}

body.dark .voice-name {
  color: #6366f1;
}

body.dark .voice-name:hover {
  background-color: rgba(99, 102, 241, 0.1);
}

.item-value-edit {
  padding: 10px;
  background-color: #f9fafb;
  border-radius: 8px;
  border: 1px solid #e5e7eb;
}

body.dark .item-value-edit {
  background-color: #374151;
  border-color: #4b5563;
}

.style-prompt-text,
.voice-id-note {
  font-size: 12px;
  color: #6b7280;
  margin-left: 8px;
  margin-right: 10px;
}

body.dark .style-prompt-text,
body.dark .voice-id-note {
  color: #9ca3af;
}

/* 选择器容器样式 */
.style-selector-container,
.voice-selector-container {
  max-height: 300px;
  overflow-y: auto;
  margin-top: 10px;
  border: 1px solid #ebeef5;
  border-radius: 8px;
  background-color: #fafafa;
  padding: 10px;
}

body.dark .style-selector-container,
body.dark .voice-selector-container {
  border-color: var(--border-color);
  background-color: var(--bg-tertiary);
}

/* 添加动画延迟类 */
.animation-delay-1 {
  animation-delay: 0.1s;
}

/* 音效播放按钮样式 */
.voice-play-button {
  width: 28px;
  height: 28px;
  border-radius: 50%;
  background-color: rgba(64, 160, 255, 0.5);
  color: white;
  display: flex;
  align-items: center;
  justify-content: center;
  transition: all 0.3s ease;
  flex-shrink: 0;
  position: relative;
  box-shadow: 0 2px 6px rgba(64, 158, 255, 0.3);
  cursor: pointer;
  margin-left: 8px;
}

.voice-play-button:hover {
  transform: scale(1.1);
  background-color: #409eff;
  box-shadow: 0 4px 10px rgba(64, 158, 255, 0.4);
}

/* 播放状态 */
.voice-play-button.playing {
  background-color: #67c23a;
  box-shadow: 0 2px 6px rgba(103, 194, 58, 0.3);
}

.voice-play-button.playing:hover {
  background-color: #85ce61;
  box-shadow: 0 4px 10px rgba(103, 194, 58, 0.4);
}

/* 波形动画 */
.wave-animation {
  position: absolute;
  left: 50%;
  top: -14px;
  transform: translateX(-50%);
  display: flex;
  align-items: flex-end;
  height: 12px;
  width: 14px;
  gap: 2px;
}

.wave-bar {
  width: 2px;
  background-color: #67c23a;
  border-radius: 1px;
  animation: waveAnimation 0.8s infinite ease-in-out;
}

.wave-bar:nth-child(1) {
  height: 5px;
  animation-delay: 0s;
}

.wave-bar:nth-child(2) {
  height: 8px;
  animation-delay: 0.2s;
}

.wave-bar:nth-child(3) {
  height: 6px;
  animation-delay: 0.4s;
}

.wave-bar:nth-child(4) {
  height: 7px;
  animation-delay: 0.6s;
}

@keyframes waveAnimation {

  0%,
  100% {
    transform: scaleY(0.6);
  }

  50% {
    transform: scaleY(1);
  }
}

body.dark .voice-play-button {
  background-color: rgba(64, 160, 255, 0.3);
}

body.dark .voice-play-button:hover {
  background-color: rgba(64, 160, 255, 0.8);
}

body.dark .voice-play-button.playing {
  background-color: rgba(103, 194, 58, 0.6);
}

body.dark .voice-play-button.playing:hover {
  background-color: rgba(103, 194, 58, 0.8);
}

.script-content {
  font-size: 16px;
  color: #334155;
  padding: 14px;
  background-color: #f8fafc55;
  border-radius: 8px;
  border-left: 1px solid #6366f1;
  text-align: left;
  white-space: pre-wrap;
  overflow-wrap: break-word;
  /* max-height: 800px; */
  overflow-y: auto;
  font-family: 'Courier New', Courier, monospace;
  line-height: 1.5;
}

body.dark .script-content {
  color: #e5e7eb;
  background-color: #37415127;
}

.copy-btn {
  width: 28px;
  height: 28px;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  transition: all 0.3s ease;
  background-color: #4f46e5;
  color: white;
  margin-left: auto;
  box-shadow: 0 2px 6px rgba(0, 0, 0, 0.1);
}

.copy-btn:hover {
  transform: scale(1.1);
  background-color: #4338ca;
}

body.dark .copy-btn {
  background-color: #6366f1;
}

body.dark .copy-btn:hover {
  background-color: #818cf8;
}

/* 创作参考图样式 */
.reference-images-container {
  padding: 10px 14px;
  background-color: #f8fafc55;
  border-radius: 8px;
  border-left: 1px solid #6366f1;
}

body.dark .reference-images-container {
  background-color: #37415129;
}

.reference-images-grid {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(120px, 1fr));
  gap: 12px;
}

.reference-image-item {
  position: relative;
  aspect-ratio: 1;
  border-radius: 8px;
  overflow: hidden;
  cursor: pointer;
  transition: all 0.3s ease;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.reference-image-item:hover {
  transform: translateY(-2px);
  box-shadow: 0 4px 16px rgba(99, 102, 241, 0.2);
}

.reference-image {
  width: 100%;
  height: 100%;
  object-fit: cover;
  transition: transform 0.3s ease;
}

.reference-image-item:hover .reference-image {
  transform: scale(1.05);
}

.image-overlay {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: rgba(0, 0, 0, 0.5);
  display: flex;
  align-items: center;
  justify-content: center;
  opacity: 0;
  transition: opacity 0.3s ease;
}

.reference-image-item:hover .image-overlay {
  opacity: 1;
}

.preview-icon {
  color: white;
  font-size: 24px;
}

body.dark .reference-image-item {
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.3);
}

body.dark .reference-image-item:hover {
  box-shadow: 0 4px 16px rgba(99, 102, 241, 0.4);
}

/* Markdown 内容样式 */
:deep(.markdown-content) {
  font-size: 14px;
  color: #334155;
}

:deep(.markdown-content p) {
  margin: 8px 0;
  line-height: 1.6;
}

:deep(.markdown-content h1),
:deep(.markdown-content h2),
:deep(.markdown-content h3),
:deep(.markdown-content h4),
:deep(.markdown-content h5),
:deep(.markdown-content h6) {
  margin-top: 16px;
  margin-bottom: 8px;
  font-weight: 600;
  line-height: 1.25;
}

:deep(.markdown-content h1) {
  font-size: 20px;
}

:deep(.markdown-content h2) {
  font-size: 18px;
}

:deep(.markdown-content h3) {
  font-size: 16px;
}

:deep(.markdown-content h4),
:deep(.markdown-content h5),
:deep(.markdown-content h6) {
  font-size: 14px;
}

:deep(.markdown-content ul),
:deep(.markdown-content ol) {
  margin: 8px 0;
  padding-left: 20px;
}

:deep(.markdown-content li) {
  margin: 4px 0;
}

:deep(.markdown-content blockquote) {
  margin: 8px 0;
  padding: 0 16px;
  color: #666;
  border-left: 4px solid #ddd;
}

:deep(.markdown-content pre) {
  margin: 8px 0;
  padding: 12px;
  background-color: #f6f8fa;
  border-radius: 6px;
  overflow-x: auto;
}

:deep(.markdown-content code) {
  font-family: 'Courier New', Courier, monospace;
  background-color: #f6f8fa;
  padding: 2px 4px;
  border-radius: 3px;
  font-size: 90%;
}

:deep(.markdown-content pre code) {
  background-color: transparent;
  padding: 0;
  border-radius: 0;
  font-size: 100%;
}

:deep(.markdown-content a) {
  color: #0366d6;
  text-decoration: none;
}

:deep(.markdown-content a:hover) {
  text-decoration: underline;
}

:deep(.markdown-content table) {
  margin: 8px 0;
  border-collapse: collapse;
  width: 100%;
}

:deep(.markdown-content table th),
:deep(.markdown-content table td) {
  padding: 6px 13px;
  border: 1px solid #dfe2e5;
}

:deep(.markdown-content table th) {
  background-color: #f6f8fa;
  font-weight: 600;
}

:deep(.markdown-content table tr:nth-child(2n)) {
  background-color: #f8f8f8;
}

:deep(.markdown-content img) {
  max-width: 100%;
  box-sizing: border-box;
  border-radius: 4px;
}

:deep(.markdown-content hr) {
  height: 1px;
  margin: 16px 0;
  background-color: #e1e4e8;
  border: none;
}

/* 响应式调整 */
@media (max-width: 768px) {
  .project-idea-container {
    padding: 12px;
  }

  .section-title {
    font-size: 16px;
  }

  .info-grid {
    /* grid-template-columns: repeat(auto-fit, minmax(240px, 1fr)); */
    gap: 12px;
  }

  .info-item {
    flex-direction: column;
    align-items: flex-start;
    justify-content: flex-start;
  }

  .info-label {
    font-size: 13px;
  }

  .info-value,
  .theme-value {
    font-size: 14px;
    padding: 8px 12px;
  }

  /* 移动设备上缩短动画时间 */
  .design-section {
    animation-duration: 0.4s;
  }

  /* 减小动画移动距离 */
  @keyframes section-appear {
    from {
      opacity: 0;
      transform: translateY(10px);
    }

    to {
      opacity: 1;
      transform: translateY(0);
    }
  }

  .script-content {
    font-size: 14px;
    padding: 10px;
    max-height: 300px;
  }

  :deep(.markdown-content) {
    font-size: 13px;
  }

  :deep(.markdown-content h1) {
    font-size: 18px;
  }

  :deep(.markdown-content h2) {
    font-size: 16px;
  }

  :deep(.markdown-content h3),
  :deep(.markdown-content h4),
  :deep(.markdown-content h5),
  :deep(.markdown-content h6) {
    font-size: 14px;
  }

  /* 移动端参考图样式调整 */
  .reference-images-grid {
    grid-template-columns: repeat(auto-fill, minmax(100px, 1fr));
    gap: 8px;
  }

  .reference-image-item {
    border-radius: 6px;
  }

  .preview-icon {
    font-size: 20px;
  }
}

/* 添加对无动画设置的支持 */
@media (prefers-reduced-motion: reduce) {
  .design-section {
    animation: none;
    opacity: 1;
    transform: none;
  }
}

/* 提示词弹框样式 */
.prompt-modal {
  position: fixed;
  top: 0;
  left: 0;
  width: 100vw;
  height: 100vh;
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 2000;
}

.prompt-backdrop {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background-color: rgba(0, 0, 0, 0.5);
  backdrop-filter: blur(4px);
  z-index: 2000;
  cursor: pointer;
}

.prompt-modal-container {
  position: fixed;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  width: 90%;
  max-width: 1000px;
  max-height: 80vh;
  background-color: white;
  border-radius: 16px;
  box-shadow: 0 10px 30px rgba(0, 0, 0, 0.2);
  z-index: 2001;
  display: flex;
  flex-direction: column;
  overflow: hidden;
  animation: fadeIn 0.3s ease;
  transition: background-color 0.3s;
}

@keyframes fadeIn {
  from {
    opacity: 0;
    transform: translate(-50%, -45%);
  }
  to {
    opacity: 1;
    transform: translate(-50%, -50%);
  }
}

body.dark .prompt-modal-container {
  background-color: var(--bg-card, #1a202c);
  box-shadow: 0 10px 30px rgba(0, 0, 0, 0.4);
}

.prompt-modal-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 16px 20px;
  background: linear-gradient(135deg, #6366f1, #4f46e5);
  color: white;
  font-size: 16px;
  font-weight: 600;
  position: relative;
  border-bottom: 1px solid rgba(255, 255, 255, 0.1);
}

.prompt-modal-header h3 {
  margin: 0;
  color: white;
  font-size: 18px;
}

.close-icon {
  cursor: pointer;
  font-size: 20px;
  color: rgba(255, 255, 255, 0.8);
  transition: color 0.3s ease;
}

.close-icon:hover {
  color: white;
  transform: scale(1.1);
}

.prompt-modal-body {
  flex-grow: 1;
  overflow-y: auto;
  padding: 20px;
  background-color: white;
  transition: background-color 0.3s;
}

body.dark .prompt-modal-body {
  background-color: var(--bg-card, #1a202c);
}

.prompt-modal-content {
  display: flex;
  flex-direction: column;
  gap: 12px;
}

.prompt-label {
  display: flex;
  align-items: center;
  gap: 8px;
  font-size: 16px;
  color: #1e293b;
  font-weight: 600;
  position: relative;
}

.prompt-label .el-icon {
  font-size: 18px;
  color: #6366f1;
}

body.dark .prompt-label {
  color: #e2e8f0;
}

body.dark .prompt-label .el-icon {
  color: #818cf8;
}

.prompt-full-content {
  font-size: 16px;
  color: #334155;
  padding: 14px;
  background-color: #f8fafc55;
  border-radius: 8px;
  border-left: 1px solid #6366f1;
  text-align: left;
  white-space: pre-wrap;
  overflow-wrap: break-word;
  font-family: 'Courier New', Courier, monospace;
  line-height: 1.5;
  max-height: calc(80vh - 200px);
  overflow-y: auto;
}

body.dark .prompt-full-content {
  color: #e5e7eb;
  background-color: #37415127;
}

/* 响应式调整 */
@media (max-width: 768px) {
  .prompt-modal-container {
    width: 95%;
    max-height: 90vh;
    margin: 0;
  }

  .prompt-modal-body {
    padding: 16px;
  }

  .prompt-full-content {
    font-size: 14px;
    padding: 10px;
    max-height: calc(90vh - 160px);
  }
}
</style>