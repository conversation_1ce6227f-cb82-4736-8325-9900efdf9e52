<template>
  <div v-if="visible" class="prompt-optimization-dialog-overlay">
    <div class="prompt-optimization-dialog" @click.stop>
      <div class="card-decoration top-left"></div>
      <div class="card-decoration top-right"></div>
      <div class="card-decoration bottom-left"></div>
      <div class="card-decoration bottom-right"></div>
      
      <div class="dialog-header">
        <h3 class="dialog-title">创作确认</h3>
        <button class="close-button" @click="handleCancel">×</button>
      </div>
      
      <div class="dialog-content">
        <div class="loading-section" v-if="isOptimizing">
          <div class="loading-spinner"></div>
          <p class="loading-text">正在优化您的创意内容，请稍候...</p>
        </div>
        
        <div class="optimization-result" v-else-if="optimizationData">
          <!-- 核心信息部分 -->
          <div class="section core-info-section">
            <h4 class="section-title">
              <span class="title-icon">🎯</span>
              核心信息
            </h4>
            <div class="core-info-content">
              <div class="topic-analysis">
                <label>主题分析</label>
                <div class="editable-content"
                     contenteditable="true"
                     @blur="updateOptimizationData('core_info.topic_analysis', $event.target.textContent)"
                     v-html="optimizationData.core_info?.topic_analysis || ''">
                </div>
              </div>
              <div class="audience-insights">
                <label>受众洞察</label>
                <div class="editable-content"
                     contenteditable="true"
                     @blur="updateOptimizationData('core_info.audience_insights', $event.target.textContent)"
                     v-html="optimizationData.core_info?.audience_insights || ''">
                </div>
              </div>
            </div>
          </div>
          
          <!-- 风格定位部分 -->
          <div class="section style-positioning-section">
            <h4 class="section-title">
              <span class="title-icon">🎨</span>
              风格定位
            </h4>
            <div class="style-positioning-content">
              <div class="style-item">
                <label>内容风格</label>
                <div class="editable-content"
                     contenteditable="true"
                     @blur="updateOptimizationData('style_positioning.content_style', $event.target.textContent)"
                     v-html="optimizationData.style_positioning?.content_style || ''">
                </div>
              </div>
              <div class="style-item">
                <label>视觉色调</label>
                <div class="editable-content"
                     contenteditable="true"
                     @blur="updateOptimizationData('style_positioning.visual_tone', $event.target.textContent)"
                     v-html="optimizationData.style_positioning?.visual_tone || ''">
                </div>
              </div>
              <div class="style-item">
                <label>音频风格</label>
                <div class="editable-content"
                     contenteditable="true"
                     @blur="updateOptimizationData('style_positioning.audio_style', $event.target.textContent)"
                     v-html="optimizationData.style_positioning?.audio_style || ''">
                </div>
              </div>
            </div>
          </div>

          <!-- 视频规划部分 -->
          <div class="section video-planning-section" v-if="optimizationData.video_planning">
            <h4 class="section-title">
              <span class="title-icon">📊</span>
              视频规划
            </h4>
            <div class="video-planning-content">
              <div class="planning-item">
                <label>镜头数量</label>
                <div class="editable-content"
                     contenteditable="true"
                     @blur="updateOptimizationData('video_planning.shot_count', parseInt($event.target.textContent) || 0)"
                     v-html="optimizationData.video_planning?.shot_count || ''">
                </div>
              </div>
            </div>
          </div>

          <!-- 视频结构部分 -->
          <div class="section video-structure-section" v-if="optimizationData.video_structure && Array.isArray(optimizationData.video_structure)">
            <h4 class="section-title">
              <span class="title-icon">🎬</span>
              视频结构
            </h4>
            <div class="video-structure-content">
              <div v-for="(segment, index) in optimizationData.video_structure"
                   :key="index"
                   class="structure-segment">
                <div class="segment-header">
                  <h5 class="segment-title">{{ segment.segment }}</h5>
                  <span class="segment-duration">{{ segment.duration }}</span>
                  <!-- <button class="remove-segment-btn" @click="removeVideoSegment(index)">×</button> -->
                </div>

                <div class="segment-content">
                  <div class="detail-item">
                    <label>视觉描述</label>
                    <div class="editable-content"
                         contenteditable="true"
                         @blur="updateVideoSegment(index, 'visuals', $event.target.textContent)"
                         v-html="segment.visuals || ''">
                    </div>
                  </div>
                  <div class="detail-item">
                    <label>脚本内容</label>
                    <div class="editable-content script-content"
                         contenteditable="true"
                         @blur="updateVideoSegment(index, 'script', $event.target.textContent)"
                         v-html="segment.script || ''">
                    </div>
                  </div>
                </div>
              </div>

              <!-- <button class="add-segment-btn" @click="addVideoSegment">+ 添加片段</button> -->
            </div>
          </div>
        </div>
        
        <div class="error-section" v-else-if="errorMessage">
          <div class="error-icon">⚠️</div>
          <p class="error-text">{{ errorMessage }}</p>
          <button class="retry-btn" @click="retryOptimization">重试</button>
        </div>
      </div>
      
      <div class="dialog-footer">
        <!-- <button class="btn btn-secondary" @click="handleCancel" :disabled="isOptimizing">
          取消
        </button> -->
        <GenerateButton
          :text="isOptimizing ? '优化中...' : '确认并开始创作'"
          :loading="isOptimizing"
          :disabled="isOptimizing || !optimizationData"
          :credits="getCreditsText()"
          @click="handleConfirm"
        />
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref, watch, computed } from 'vue'
import { ElMessage } from 'element-plus'
import { promptOptimization } from '@/api/auth.js'
import GenerateButton from './videoEdit/GenerateButton.vue'

const props = defineProps({
  visible: {
    type: Boolean,
    default: false
  },
  creativeText: {
    type: String,
    default: ''
  },
  selectedStylePrompt: {
    type: String,
    default: null
  }
})

const emit = defineEmits(['update:visible', 'confirm', 'cancel'])

// 状态管理
const isOptimizing = ref(false)
const optimizationData = ref({
  "core_info": {
    "topic_analysis": "水煮鱼教学视频旨在通过清晰、易懂的步骤展示水煮鱼的制作过程。核心信息包括食材准备、关键烹饪技巧（如鱼肉处理、辣椒和花椒的炒制、热油浇淋）、以及最终的成品展示。视频需强调家常、美味、易学。",
    "audience_insights": "目标受众是居家烹饪爱好者，尤其对川菜或辛辣口味有兴趣的初学者和有一定基础的厨艺爱好者。他们可能关心：食材的易购性、操作步骤的详细度、调料的配比、如何避免常见的烹饪误区（如鱼肉不嫩、味道不香），以及最终成品的色香味展现。他们希望视频教程简洁明了，具有实操性，并能激发食欲。"
  },
  "style_positioning": {
    "content_style": "亲切、教学向、实用主义。文案应口语化，步骤清晰，适当加入小贴士和强调口感的形容词，营造轻松愉快的学习氛围，同时强调美味与诱惑力。",
    "visual_tone": "[明亮、清新、干净的自然光][高饱和度，中等对比度][写实特写，突出食材和烹饪细节][暖色调为主，如橙红色、亮黄色、木质色，辅以食材本身的鲜亮色彩][色彩情绪：食欲、温馨、家庭]，运用特写镜头，强调食材的新鲜和烹饪过程的细节。",
    "audio_style": "[背景音乐类型：轻快、活泼的纯音乐或生活感强的节奏；节奏：中速][音效类别：食材处理声（切菜、倒油）、烹饪声（油入锅噼啪声、翻炒声）、咀嚼声（可选）；功能：增强真实感和沉浸感][音量层次：背景音乐适中，对话清晰，音效在关键步骤突出][情绪氛围营造：轻松、愉悦、诱人食欲][与视觉内容的同步性：音效应与画面动作精准同步，背景音乐与视频节奏和情绪变化协调。"
  },
  "video_planning": {
    "shot_count": 25
  },
  "video_structure": [
    {
      "segment": "开头",
      "duration": "10秒",
      "visuals": "诱人的水煮鱼成品特写，热气腾腾，香气四溢。快速闪过几个关键食材的特写。主播微笑着出镜。",
      "script": "（背景音乐响起）哇塞！这碗麻辣鲜香的水煮鱼，是不是光看着就流口水了？今天，我手把手教你，在家也能做出比饭店还好吃的“水煮鱼”！保证让你轻松掌握，成就感满满！"
    },
    {
      "segment": "主体-食材准备",
      "duration": "40秒",
      "visuals": "桌面摆放整齐的食材特写（鱼、豆芽、辣椒、花椒、葱姜蒜等）。鱼肉切片及腌制过程（加入料酒、盐、淀粉、蛋清抓匀）。配菜的处理（豆芽焯水、辣椒切段）。",
      "script": "首先，准备好我们的灵魂食材：鲜活的草鱼或巴沙鱼。将鱼肉片成薄片，加入料酒、少量盐、淀粉和关键的蛋清，抓匀，锁住鱼肉的鲜嫩！豆芽焯水备用，干辣椒和花椒也准备好，这是水煮鱼的精髓所在。"
    },
    {
      "segment": "主体-底料炒制与煮鱼",
      "duration": "60秒",
      "visuals": "锅中热油，爆香葱姜蒜、豆瓣酱、干辣椒、花椒。倒入适量水或高汤煮开。下入腌好的鱼片，轻轻滑散，煮至变色。将豆芽铺在碗底，捞出鱼片放入碗中。",
      "script": "锅中倒油，中小火爆香葱姜蒜，接着加入郫县豆瓣酱，炒出红油，再放入干辣椒段和花椒粒，炒出香气。倒入足量开水，煮沸后下入我们腌制好的鱼片。记住，水开后下鱼片，轻轻滑散，煮至鱼肉刚刚变色，即可关火。煮得恰到好处，鱼肉才能嫩滑如丝！将煮好的鱼片连同汤汁一起倒入铺有豆芽的大碗中。"
    },
    {
      "segment": "主体-灵魂热油浇淋",
      "duration": "30秒",
      "visuals": "鱼片上铺满蒜末、干辣椒段、花椒。另起锅烧热油至冒烟，淋在调料上，发出“滋啦”声，香气瞬间迸发。近距离特写。",
      "script": "这最后一步，是水煮鱼的灵魂所在！在鱼片上铺上满满的蒜末、干辣椒段和花椒粒。另起一锅，烧热一大勺食用油，烧到微微冒烟，然后，将这滚烫的热油，瞬间淋到碗里的辣椒花椒和蒜末上！听！这美妙的‘滋啦’声，瞬间香气扑鼻，麻辣鲜香被瞬间激发出来，太诱人了！"
    },
    {
      "segment": "结尾",
      "duration": "10秒",
      "visuals": "成品水煮鱼全景，特写鱼肉的鲜嫩和汤汁的红亮。主播品尝，露出满足的表情。字幕提示关注和收藏。",
      "script": "看！这色泽红亮，香气四溢的水煮鱼就大功告成了！鱼肉滑嫩，汤汁麻辣鲜香，简直不要太下饭！你学会了吗？赶紧动手试试吧！别忘了点赞、关注和收藏，更多美食等你解锁哦！"
    }
  ]
})
const errorMessage = ref('')

// 监听对话框显示状态，自动调用优化API
watch(() => props.visible, (newVisible) => {
  if (newVisible && props.creativeText) {
    startOptimization()
  } else if (!newVisible) {
    // 重置状态
    optimizationData.value = null
    errorMessage.value = ''
    isOptimizing.value = false
  }
})

// 开始优化
const startOptimization = async () => {
  if (!props.creativeText) {
    errorMessage.value = '请输入创意内容'
    return
  }
  
  isOptimizing.value = true
  errorMessage.value = ''

  try {
    // const response = await promptOptimization(props.creativeText)
    // if (response && response.success) {
    //   optimizationData.value = response.data
    // } else {
    //   errorMessage.value = response?.errMessage || '优化失败，请重试'
    // }
  } catch (error) {
    console.error('Prompt优化失败:', error)
    errorMessage.value = '网络错误，请重试'
  } finally {
    isOptimizing.value = false
  }
}

// 重试优化
const retryOptimization = () => {
  startOptimization()
}

// 更新优化数据
const updateOptimizationData = (path, value) => {
  if (!optimizationData.value) return
  
  const keys = path.split('.')
  let current = optimizationData.value
  
  for (let i = 0; i < keys.length - 1; i++) {
    if (!current[keys[i]]) {
      current[keys[i]] = {}
    }
    current = current[keys[i]]
  }
  
  current[keys[keys.length - 1]] = value
}

// 更新视频片段
const updateVideoSegment = (index, field, value) => {
  if (optimizationData.value?.video_structure && Array.isArray(optimizationData.value.video_structure)) {
    if (optimizationData.value.video_structure[index]) {
      optimizationData.value.video_structure[index][field] = value
    }
  }
}

// 注意：添加和删除视频片段的功能已被注释，因为按钮被隐藏了

// 计算积分消耗
const getCreditsText = () => {
  if (!optimizationData.value?.video_planning?.shot_count) {
    return '⚡ 0'
  }
  const shotCount = optimizationData.value.video_planning.shot_count
  const credits = shotCount * 30
  return `⚡ ${credits}`
}

// 处理确认
const handleConfirm = () => {
  if (!optimizationData.value) {
    ElMessage.warning('请等待优化完成')
    return
  }
  
  emit('confirm', optimizationData.value)
  emit('update:visible', false)
}

// 处理取消
const handleCancel = () => {
  emit('cancel')
  emit('update:visible', false)
}
</script>

<style scoped>
.prompt-optimization-dialog-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: rgba(0, 0, 0, 0.6);
  backdrop-filter: blur(4px);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 1000;
  padding: 20px;
  box-sizing: border-box;
}

.prompt-optimization-dialog {
  background: rgba(255, 255, 255, 0.95);
  border-radius: 24px;
  box-shadow: 0 20px 60px rgba(0, 0, 0, 0.15);
  max-width: 800px;
  width: 100%;
  max-height: 92vh;
  position: relative;
  animation: dialog-appear 0.3s cubic-bezier(0.34, 1.56, 0.64, 1);
  text-align: left;
}

label{
  text-align: left;
}

body.dark .prompt-optimization-dialog {
  background: rgba(30, 30, 30, 0.95);
  box-shadow: 0 20px 60px rgba(0, 0, 0, 0.3);
}

@keyframes dialog-appear {
  0% {
    opacity: 0;
    transform: scale(0.9) translateY(20px);
  }
  100% {
    opacity: 1;
    transform: scale(1) translateY(0);
  }
}

/* 装饰元素 */
.card-decoration {
  position: absolute;
  border-radius: 50%;
  background: linear-gradient(135deg, rgba(99, 102, 241, 0.3), rgba(138, 92, 246, 0.1));
  filter: blur(20px);
  pointer-events: none;
  z-index: 0;
}

.top-left {
  top: -20px;
  left: -20px;
  width: 80px;
  height: 80px;
  background: linear-gradient(135deg, rgba(59, 130, 246, 0.3), rgba(99, 102, 241, 0.1));
}

.top-right {
  top: -30px;
  right: -30px;
  width: 100px;
  height: 100px;
  background: linear-gradient(135deg, rgba(139, 92, 246, 0.3), rgba(168, 85, 247, 0.1));
}

.bottom-left {
  bottom: -40px;
  left: -20px;
  width: 150px;
  height: 150px;
  background: linear-gradient(135deg, rgba(6, 182, 212, 0.3), rgba(20, 184, 166, 0.1));
}

.bottom-right {
  bottom: -30px;
  right: -30px;
  width: 80px;
  height: 80px;
  background: linear-gradient(135deg, rgba(20, 184, 166, 0.3), rgba(6, 182, 212, 0.1));
}

body.dark .card-decoration {
  opacity: 0.6;
}

/* 对话框头部 */
.dialog-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 16px 24px 0;
  position: relative;
  z-index: 1;
}

.dialog-title {
  font-size: 20px;
  font-weight: 600;
  color: #1e293b;
  margin: 0;
  background: linear-gradient(135deg, #6366f1, #8b5cf6);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
}

body.dark .dialog-title {
  color: #f1f5f9;
}

.close-button {
  width: 36px;
  height: 36px;
  border-radius: 18px;
  background-color: rgba(148, 163, 184, 0.1);
  border: none;
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  font-size: 20px;
  color: #64748b;
  transition: all 0.2s ease;
}

.close-button:hover {
  background-color: rgba(239, 68, 68, 0.1);
  color: #ef4444;
  transform: rotate(90deg);
}

body.dark .close-button {
  background-color: rgba(148, 163, 184, 0.1);
  color: #94a3b8;
}

body.dark .close-button:hover {
  background-color: rgba(239, 68, 68, 0.2);
  color: #f87171;
}

/* 对话框内容 */
.dialog-content {
  padding: 16px 24px;
  position: relative;
  z-index: 1;
}

/* 加载状态 */
.loading-section {
  text-align: left;
  padding: 60px 20px;
  display: flex;
  align-items: center;
  gap: 16px;
}

.loading-spinner {
  width: 32px;
  height: 32px;
  border: 3px solid #e2e8f0;
  border-top: 3px solid #6366f1;
  border-radius: 50%;
  animation: spin 1s linear infinite;
  flex-shrink: 0;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

.loading-text {
  font-size: 16px;
  color: #64748b;
  margin: 0;
}

body.dark .loading-text {
  color: #94a3b8;
}

body.dark .loading-spinner {
  border-color: #374151;
  border-top-color: #818cf8;
}

/* 优化结果 */
.optimization-result {
  max-height: 65vh;
  overflow-y: auto;
  padding-right: 8px;
}

.section {
  margin-bottom: 20px;
  background: rgba(248, 250, 252, 0.8);
  border-radius: 12px;
  padding: 16px;
  border: 1px solid rgba(226, 232, 240, 0.5);
}

body.dark .section {
  background: rgba(30, 41, 59, 0.5);
  border-color: rgba(71, 85, 105, 0.3);
}

.section-title {
  display: flex;
  align-items: center;
  gap: 8px;
  font-size: 16px;
  font-weight: 600;
  color: #1e293b;
  margin: 0 0 12px 0;
}

body.dark .section-title {
  color: #f1f5f9;
}

.title-icon {
  font-size: 16px;
}

/* 可编辑内容 */
.editable-content {
  background: rgba(255, 255, 255, 0.8);
  border: 1px solid #e2e8f0;
  border-radius: 6px;
  padding: 8px 12px;
  min-height: 18px;
  line-height: 1.5;
  color: #334155;
  transition: all 0.2s ease;
  cursor: text;
  font-size: 14px;
}

.editable-content:hover {
  border-color: #a5b4fc;
  background: rgba(255, 255, 255, 0.9);
}

.editable-content:focus {
  outline: none;
  border-color: #6366f1;
  box-shadow: 0 0 0 3px rgba(99, 102, 241, 0.1);
}

body.dark .editable-content {
  background: rgba(51, 65, 85, 0.5);
  border-color: #475569;
  color: #e2e8f0;
}

body.dark .editable-content:hover {
  border-color: #818cf8;
  background: rgba(51, 65, 85, 0.7);
}

body.dark .editable-content:focus {
  border-color: #818cf8;
  box-shadow: 0 0 0 3px rgba(129, 140, 248, 0.1);
}

/* 核心信息内容 */
.core-info-content > div {
  margin-bottom: 12px;
}

.core-info-content > div:last-child {
  margin-bottom: 0;
}

.core-info-content label {
  display: block;
  font-weight: 500;
  color: #475569;
  margin-bottom: 6px;
  font-size: 13px;
}

body.dark .core-info-content label {
  color: #cbd5e1;
}

/* 关键要点 */
.key-points-list {
  display: flex;
  flex-direction: column;
  gap: 12px;
}

.key-point-item {
  display: flex;
  align-items: flex-start;
  gap: 12px;
  background: rgba(255, 255, 255, 0.6);
  border-radius: 8px;
  padding: 12px;
  border: 1px solid rgba(226, 232, 240, 0.5);
}

body.dark .key-point-item {
  background: rgba(51, 65, 85, 0.3);
  border-color: rgba(71, 85, 105, 0.3);
}

.point-number {
  background: linear-gradient(135deg, #6366f1, #8b5cf6);
  color: white;
  width: 24px;
  height: 24px;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 12px;
  font-weight: 600;
  flex-shrink: 0;
  margin-top: 2px;
}

.key-point-item .editable-content {
  flex: 1;
  margin: 0;
  background: transparent;
  border: 1px dashed transparent;
  padding: 8px 12px;
}

.key-point-item .editable-content:hover {
  border-color: #a5b4fc;
  background: rgba(255, 255, 255, 0.8);
}

body.dark .key-point-item .editable-content:hover {
  background: rgba(51, 65, 85, 0.6);
}

.remove-point-btn {
  width: 24px;
  height: 24px;
  border-radius: 50%;
  background: rgba(239, 68, 68, 0.1);
  border: none;
  color: #ef4444;
  cursor: pointer;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 16px;
  transition: all 0.2s ease;
  flex-shrink: 0;
}

.remove-point-btn:hover {
  background: rgba(239, 68, 68, 0.2);
  transform: scale(1.1);
}

.add-point-btn {
  background: linear-gradient(135deg, #10b981, #059669);
  color: white;
  border: none;
  border-radius: 8px;
  padding: 10px 16px;
  font-size: 14px;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.2s ease;
  align-self: flex-start;
}

.add-point-btn:hover {
  transform: translateY(-1px);
  box-shadow: 0 4px 12px rgba(16, 185, 129, 0.3);
}

/* 风格定位内容 */
.style-positioning-content .style-item {
  margin-bottom: 12px;
}

.style-positioning-content .style-item:last-child {
  margin-bottom: 0;
}

.style-positioning-content label {
  display: block;
  font-weight: 500;
  color: #475569;
  margin-bottom: 6px;
  font-size: 13px;
}

body.dark .style-positioning-content label {
  color: #cbd5e1;
}

/* 视频规划内容 */
.video-planning-content .planning-item {
  margin-bottom: 12px;
}

.video-planning-content .planning-item:last-child {
  margin-bottom: 0;
}

.video-planning-content label {
  display: block;
  font-weight: 500;
  color: #475569;
  margin-bottom: 6px;
  font-size: 13px;
}

body.dark .video-planning-content label {
  color: #cbd5e1;
}

/* 视频结构内容 */
.video-structure-content .structure-segment {
  margin-bottom: 16px;
  background: rgba(255, 255, 255, 0.6);
  border-radius: 8px;
  padding: 14px;
  border: 1px solid rgba(226, 232, 240, 0.5);
}

body.dark .video-structure-content .structure-segment {
  background: rgba(51, 65, 85, 0.3);
  border-color: rgba(71, 85, 105, 0.3);
}

.segment-header {
  display: flex;
  flex-direction: row;
  margin-bottom: 10px;
  gap: 12px;
}

.segment-title {
  font-size: 14px;
  font-weight: 600;
  color: #1e293b;
  margin: 0;
  display: flex;
  align-items: center;
  gap: 6px;
}

.segment-title::before {
  content: '';
  width: 3px;
  height: 12px;
  background: linear-gradient(135deg, #6366f1, #8b5cf6);
  border-radius: 2px;
}

body.dark .segment-title {
  color: #f1f5f9;
}

.segment-duration {
  font-weight: 600;
  color: #6366f1;
  font-size: 12px;
  background: rgba(99, 102, 241, 0.1);
  padding: 2px 6px;
  border-radius: 3px;
}

body.dark .segment-duration {
  color: #818cf8;
  background: rgba(129, 140, 248, 0.1);
}

.structure-details .detail-item {
  margin-bottom: 10px;
}

.structure-details .detail-item:last-child {
  margin-bottom: 0;
}

.structure-details label {
  display: block;
  font-weight: 500;
  color: #475569;
  margin-bottom: 4px;
  font-size: 12px;
}

body.dark .structure-details label {
  color: #cbd5e1;
}

/* 视频片段 */
.segments-list {
  display: flex;
  flex-direction: column;
  gap: 16px;
}

.segment-item {
  background: rgba(248, 250, 252, 0.8);
  border-radius: 8px;
  padding: 16px;
  border: 1px solid rgba(226, 232, 240, 0.5);
}

body.dark .segment-item {
  background: rgba(30, 41, 59, 0.5);
  border-color: rgba(71, 85, 105, 0.3);
}


.segment-time {
  font-weight: 600;
  color: #6366f1;
  font-size: 14px;
  background: rgba(99, 102, 241, 0.1);
  padding: 4px 8px;
  border-radius: 4px;
}

body.dark .segment-time {
  color: #818cf8;
  background: rgba(129, 140, 248, 0.1);
}

.remove-segment-btn {
  width: 20px;
  height: 20px;
  border-radius: 50%;
  background: rgba(239, 68, 68, 0.1);
  border: none;
  color: #ef4444;
  cursor: pointer;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 14px;
  transition: all 0.2s ease;
}

.remove-segment-btn:hover {
  background: rgba(239, 68, 68, 0.2);
  transform: scale(1.1);
}

.segment-content .detail-item {
  margin-bottom: 8px;
}

.segment-content .detail-item:last-child {
  margin-bottom: 0;
}

.add-segment-btn {
  background: linear-gradient(135deg, #8b5cf6, #7c3aed);
  color: white;
  border: none;
  border-radius: 8px;
  padding: 10px 16px;
  font-size: 14px;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.2s ease;
  align-self: flex-start;
}

.add-segment-btn:hover {
  transform: translateY(-1px);
  box-shadow: 0 4px 12px rgba(139, 92, 246, 0.3);
}

/* 脚本内容特殊样式 */
.script-content {
  min-height: 40px;
  font-family: 'Courier New', monospace;
  background: rgba(248, 250, 252, 0.8) !important;
  border: 1px dashed #cbd5e1 !important;
  font-size: 13px;
}

body.dark .script-content {
  background: rgba(30, 41, 59, 0.6) !important;
  border-color: #64748b !important;
}

.script-content:hover {
  background: rgba(255, 255, 255, 0.9) !important;
  border-color: #a5b4fc !important;
}

body.dark .script-content:hover {
  background: rgba(30, 41, 59, 0.8) !important;
  border-color: #818cf8 !important;
}

/* 错误状态 */
.error-section {
  text-align: left;
  padding: 80px 20px;
  display: flex;
  align-items: center;
  gap: 16px;
  flex-wrap: wrap;
}

.error-icon {
  font-size: 32px;
  flex-shrink: 0;
}

.error-text {
  font-size: 16px;
  color: #ef4444;
  margin: 0;
  flex: 1;
}

body.dark .error-text {
  color: #f87171;
}

.retry-btn {
  background: linear-gradient(135deg, #f59e0b, #d97706);
  color: white;
  border: none;
  border-radius: 8px;
  padding: 10px 20px;
  font-size: 14px;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.2s ease;
  flex-shrink: 0;
}

.retry-btn:hover {
  transform: translateY(-1px);
  box-shadow: 0 4px 12px rgba(245, 158, 11, 0.3);
}

/* 对话框底部 */
.dialog-footer {
  display: flex;
  justify-content: center;
  gap: 12px;
  padding: 0 24px 20px;
  position: relative;
  z-index: 1;
}

/* GenerateButton 样式会自动应用，无需额外样式 */

/* 响应式设计 */
@media (max-width: 768px) {
  .prompt-optimization-dialog {
    margin: 10px;
    max-width: calc(100vw - 20px);
    border-radius: 16px;
  }

  .dialog-header,
  .dialog-content,
  .dialog-footer {
    padding-left: 20px;
    padding-right: 20px;
  }

  .dialog-title {
    font-size: 20px;
  }

  .section {
    padding: 16px;
  }

  .key-point-item {
    flex-direction: column;
    gap: 8px;
  }

  .point-number {
    align-self: flex-start;
  }

  .dialog-footer {
    flex-direction: column;
  }

  .btn {
    width: 100%;
  }
}

/* 滚动条样式 */
.optimization-result::-webkit-scrollbar {
  width: 6px;
}

.optimization-result::-webkit-scrollbar-track {
  background: rgba(226, 232, 240, 0.3);
  border-radius: 3px;
}

.optimization-result::-webkit-scrollbar-thumb {
  background: rgba(148, 163, 184, 0.5);
  border-radius: 3px;
}

.optimization-result::-webkit-scrollbar-thumb:hover {
  background: rgba(148, 163, 184, 0.7);
}

body.dark .optimization-result::-webkit-scrollbar-track {
  background: rgba(71, 85, 105, 0.3);
}

body.dark .optimization-result::-webkit-scrollbar-thumb {
  background: rgba(148, 163, 184, 0.4);
}

body.dark .optimization-result::-webkit-scrollbar-thumb:hover {
  background: rgba(148, 163, 184, 0.6);
}

/* 增强可读性的样式 */
.core-info-content > div + div,
.style-positioning-content .style-item + .style-item,
.video-planning-content .planning-item + .planning-item {
  border-top: 1px solid rgba(226, 232, 240, 0.3);
  padding-top: 12px;
}

body.dark .core-info-content > div + div,
body.dark .style-positioning-content .style-item + .style-item,
body.dark .video-planning-content .planning-item + .planning-item {
  border-top-color: rgba(71, 85, 105, 0.3);
}

/* 标签样式优化 */
label {
  font-weight: 600;
  text-transform: uppercase;
  letter-spacing: 0.5px;
  color: #64748b !important;
  font-size: 11px !important;
}

body.dark label {
  color: #94a3b8 !important;
}

/* 紧凑的视频片段布局 */
.structure-segment {
  border-left: 3px solid #6366f1;
  border-radius: 0 8px 8px 0 !important;
}

.structure-segment:nth-child(even) {
  border-left-color: #8b5cf6;
}

.structure-segment:nth-child(3n) {
  border-left-color: #06b6d4;
}

/* 确保所有文本左对齐 */
.prompt-optimization-dialog * {
  text-align: left;
}

.prompt-optimization-dialog .dialog-footer {
  text-align: right;
}

.prompt-optimization-dialog h1,
.prompt-optimization-dialog h2,
.prompt-optimization-dialog h3,
.prompt-optimization-dialog h4,
.prompt-optimization-dialog h5,
.prompt-optimization-dialog h6,
.prompt-optimization-dialog p,
.prompt-optimization-dialog div,
.prompt-optimization-dialog span,
.prompt-optimization-dialog label {
  text-align: left;
}
</style>
