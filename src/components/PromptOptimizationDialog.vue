<template>
  <div v-if="visible" class="prompt-optimization-dialog-overlay">
    <div class="prompt-optimization-dialog" @click.stop>
      <div class="card-decoration top-left"></div>
      <div class="card-decoration top-right"></div>
      <div class="card-decoration bottom-left"></div>
      <div class="card-decoration bottom-right"></div>
      
      <div class="dialog-header">
        <h3 class="dialog-title">脚本</h3>
        <button class="close-button" @click="handleCancel">×</button>
      </div>
      
      <div class="dialog-content">
        <div class="loading-section" v-if="isOptimizing">
          <div class="loading-content">
            <div class="loading-spinner"></div>
            <div class="loading-details">
              <p class="loading-text">正在为您生成创意脚本，请稍候...</p>
              <div class="loading-progress">
                <div class="progress-bar">
                  <div class="progress-fill" :style="{ width: progressPercentage + '%' }"></div>
                </div>
                <p class="countdown-text">预计还需 {{ remainingTime }} 秒</p>
              </div>
            </div>
          </div>
        </div>
        
        <div class="optimization-result" v-else-if="optimizationData">
          <!-- 核心信息部分 -->
          <div class="section core-info-section">
            <h4 class="section-title">
              <!-- <span class="title-icon">🎯</span> -->
              核心信息
            </h4>
            <div class="core-info-content">
              <div class="topic-analysis">
                <label>主题分析</label>
                <div class="editable-content"
                     contenteditable="true"
                     @blur="updateOptimizationData('core_info.topic_analysis', $event.target.textContent)"
                     v-html="optimizationData.core_info?.topic_analysis || ''">
                </div>
              </div>
              <div class="audience-insights">
                <label>受众洞察</label>
                <div class="editable-content"
                     contenteditable="true"
                     @blur="updateOptimizationData('core_info.audience_insights', $event.target.textContent)"
                     v-html="optimizationData.core_info?.audience_insights || ''">
                </div>
              </div>
            </div>
          </div>
          
          <!-- 风格定位部分 -->
          <div class="section style-positioning-section">
            <h4 class="section-title">
              <!-- <span class="title-icon">🎨</span> -->
              风格定位
            </h4>
            <div class="style-positioning-content">
              <div class="style-item">
                <label>内容风格</label>
                <div class="editable-content"
                     contenteditable="true"
                     @blur="updateOptimizationData('style_positioning.content_style', $event.target.textContent)"
                     v-html="optimizationData.style_positioning?.content_style || ''">
                </div>
              </div>
              <div class="style-item">
                <label>视觉色调</label>
                <div class="editable-content"
                     contenteditable="true"
                     @blur="updateOptimizationData('style_positioning.visual_tone', $event.target.textContent)"
                     v-html="optimizationData.style_positioning?.visual_tone || ''">
                </div>
              </div>
              <div class="style-item">
                <label>音频风格</label>
                <div class="editable-content"
                     contenteditable="true"
                     @blur="updateOptimizationData('style_positioning.audio_style', $event.target.textContent)"
                     v-html="optimizationData.style_positioning?.audio_style || ''">
                </div>
              </div>
            </div>
          </div>

          <!-- 视频规划部分 -->
          <div class="section video-planning-section" v-if="optimizationData.video_planning">
            <h4 class="section-title">
              <!-- <span class="title-icon">📊</span> -->
              视频规划
            </h4>
            <div class="video-planning-content">
              <div class="planning-item">
                <label>镜头数量</label>
                <div class="editable-content"
                     contenteditable="true"
                     @blur="updateOptimizationData('video_planning.shot_count', parseInt($event.target.textContent) || 0)"
                     v-html="optimizationData.video_planning?.shot_count || ''">
                </div>
              </div>
            </div>
          </div>

          <!-- 视频结构部分 -->
          <div class="section video-structure-section" v-if="optimizationData.video_structure && Array.isArray(optimizationData.video_structure)">
            <h4 class="section-title">
              <!-- <span class="title-icon">🎬</span> -->
              视频结构
            </h4>
            <div class="video-structure-content">
              <div v-for="(segment, index) in optimizationData.video_structure"
                   :key="index"
                   class="structure-segment">
                <div class="segment-header">
                  <h5 class="segment-title">{{ segment.segment }}</h5>
                  <span class="segment-duration">{{ segment.duration }}</span>
                  <!-- <button class="remove-segment-btn" @click="removeVideoSegment(index)">×</button> -->
                </div>

                <div class="segment-content">
                  <div class="detail-item">
                    <label>视觉描述</label>
                    <div class="editable-content"
                         contenteditable="true"
                         @blur="updateVideoSegment(index, 'visuals', $event.target.textContent)"
                         v-html="segment.visuals || ''">
                    </div>
                  </div>
                  <div class="detail-item">
                    <label>脚本内容</label>
                    <div class="editable-content script-content"
                         contenteditable="true"
                         @blur="updateVideoSegment(index, 'script', $event.target.textContent)"
                         v-html="segment.script || ''">
                    </div>
                  </div>
                </div>
              </div>

              <!-- <button class="add-segment-btn" @click="addVideoSegment">+ 添加片段</button> -->
            </div>
          </div>
        </div>
        
        <div class="error-section" v-else-if="errorMessage">
          <div class="error-content">
            <div class="error-icon-wrapper">
              <div class="error-icon">⚠️</div>
            </div>
            <div class="error-details">
              <h4 class="error-title">优化失败</h4>
              <p class="error-text">{{ errorMessage }}</p>
              <div class="error-actions">
                <button class="retry-btn" @click="retryOptimization">
                  <span class="retry-icon">🔄</span>
                  重新尝试
                </button>
              </div>
            </div>
          </div>
        </div>
      </div>
      
      <div class="dialog-footer" v-if="!errorMessage && !isOptimizing">
        <button class="btn btn-secondary" @click="handleRegenerate" :disabled="isOptimizing">
          重新生成
        </button>
        <GenerateButton
          :text="isOptimizing ? '优化中...' : '开始生成故事和分镜'"
          :loading="isOptimizing"
          :disabled="isOptimizing || !optimizationData"
          :credits="getCreditsText()"
          @click="handleConfirm"
        />
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref, watch, computed } from 'vue'
import { ElMessage } from 'element-plus'
import { promptOptimization } from '@/api/auth.js'
import GenerateButton from './videoEdit/GenerateButton.vue'

const props = defineProps({
  visible: {
    type: Boolean,
    default: false
  },
  creativeText: {
    type: String,
    default: ''
  },
  selectedStylePrompt: {
    type: String,
    default: null
  }
})

const emit = defineEmits(['update:visible', 'confirm', 'cancel'])

// 状态管理
const isOptimizing = ref(false)
const remainingTime = ref(18)
const progressPercentage = ref(0)
let countdownTimer = null
const optimizationData = ref(null)
const errorMessage = ref('')

// 监听对话框显示状态，自动调用优化API
watch(() => props.visible, (newVisible) => {
  if (newVisible && props.creativeText) {
    startOptimization()
  } else if (!newVisible) {
    // 重置状态
    stopCountdown()
    optimizationData.value = null
    errorMessage.value = ''
    isOptimizing.value = false
    remainingTime.value = 18
    progressPercentage.value = 0
  }
})

// 开始倒计时
const startCountdown = () => {
  remainingTime.value = 18
  progressPercentage.value = 0
  let totalTime = 18 * 1000 // 18秒转换为毫秒
  let elapsed = 0

  countdownTimer = setInterval(() => {
    elapsed += 100 // 每100毫秒更新一次
    remainingTime.value = Math.max(0, Math.ceil((totalTime - elapsed) / 1000))
    progressPercentage.value = Math.min(100, (elapsed / totalTime) * 100)

    if (elapsed >= totalTime) {
      clearInterval(countdownTimer)
      countdownTimer = null
    }
  }, 100) // 每100毫秒更新一次，使动画更平滑
}

// 停止倒计时
const stopCountdown = () => {
  if (countdownTimer) {
    clearInterval(countdownTimer)
    countdownTimer = null
  }
}

// 开始优化
const startOptimization = async () => {
  if (!props.creativeText) {
    errorMessage.value = '请输入创意内容'
    return
  }

  isOptimizing.value = true
  errorMessage.value = ''
  startCountdown()

  try {
    const response = await promptOptimization(props.creativeText)
    if (response && response.success && response.data) {
      optimizationData.value = JSON.parse(response.data.optimizedPrompt)
    } else {
      errorMessage.value = response?.errMessage || '优化失败，请重试'
    }
  } catch (error) {
    console.error('Prompt优化失败:', error)
    errorMessage.value = '网络错误，请重试'
  } finally {
    stopCountdown()
    isOptimizing.value = false
  }
}

// 重试优化
const retryOptimization = () => {
  startOptimization()
}

// 更新优化数据
const updateOptimizationData = (path, value) => {
  if (!optimizationData.value) return
  
  const keys = path.split('.')
  let current = optimizationData.value
  
  for (let i = 0; i < keys.length - 1; i++) {
    if (!current[keys[i]]) {
      current[keys[i]] = {}
    }
    current = current[keys[i]]
  }
  
  current[keys[keys.length - 1]] = value
}

// 更新视频片段
const updateVideoSegment = (index, field, value) => {
  if (optimizationData.value?.video_structure && Array.isArray(optimizationData.value.video_structure)) {
    if (optimizationData.value.video_structure[index]) {
      optimizationData.value.video_structure[index][field] = value
    }
  }
}

// 注意：添加和删除视频片段的功能已被注释，因为按钮被隐藏了

// 计算积分消耗
const getCreditsText = () => {
  if (!optimizationData.value?.video_planning?.shot_count) {
    return '⚡ 0'
  }
  const shotCount = optimizationData.value.video_planning.shot_count
  const credits = shotCount * 30
  return `⚡ ${credits}`
}

// 处理确认
const handleConfirm = () => {
  if (!optimizationData.value) {
    ElMessage.warning('请等待优化完成')
    return
  }
  
  emit('confirm', optimizationData.value)
  emit('update:visible', false)
}

// 处理重新生成
const handleRegenerate = () => {
  if (isOptimizing.value) {
    // 如果正在优化，停止当前优化
    stopCountdown()
    isOptimizing.value = false
  }
  // 重新开始优化
  startOptimization()
}

// 处理取消
const handleCancel = () => {
  emit('cancel')
  emit('update:visible', false)
}
</script>

<style scoped>
.prompt-optimization-dialog-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: rgba(0, 0, 0, 0.6);
  backdrop-filter: blur(4px);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 1000;
  padding: 20px;
  box-sizing: border-box;
}

.prompt-optimization-dialog {
  background: rgba(255, 255, 255, 0.95);
  border-radius: 24px;
  box-shadow: 0 20px 60px rgba(0, 0, 0, 0.15);
  max-width: 800px;
  width: 100%;
  max-height: 92vh;
  position: relative;
  animation: dialog-appear 0.3s cubic-bezier(0.34, 1.56, 0.64, 1);
  text-align: left;
}

label{
  text-align: left;
}

body.dark .prompt-optimization-dialog {
  background: rgba(30, 30, 30, 0.95);
  box-shadow: 0 20px 60px rgba(0, 0, 0, 0.3);
}

@keyframes dialog-appear {
  0% {
    opacity: 0;
    transform: scale(0.9) translateY(20px);
  }
  100% {
    opacity: 1;
    transform: scale(1) translateY(0);
  }
}

/* 装饰元素 */
.card-decoration {
  position: absolute;
  border-radius: 50%;
  background: linear-gradient(135deg, rgba(99, 102, 241, 0.3), rgba(138, 92, 246, 0.1));
  filter: blur(20px);
  pointer-events: none;
  z-index: 0;
}

.top-left {
  top: -20px;
  left: -20px;
  width: 80px;
  height: 80px;
  background: linear-gradient(135deg, rgba(59, 130, 246, 0.3), rgba(99, 102, 241, 0.1));
}

.top-right {
  top: -30px;
  right: -30px;
  width: 100px;
  height: 100px;
  background: linear-gradient(135deg, rgba(139, 92, 246, 0.3), rgba(168, 85, 247, 0.1));
}

.bottom-left {
  bottom: -40px;
  left: -20px;
  width: 150px;
  height: 150px;
  background: linear-gradient(135deg, rgba(6, 182, 212, 0.3), rgba(20, 184, 166, 0.1));
}

.bottom-right {
  bottom: -30px;
  right: -30px;
  width: 80px;
  height: 80px;
  background: linear-gradient(135deg, rgba(20, 184, 166, 0.3), rgba(6, 182, 212, 0.1));
}

body.dark .card-decoration {
  opacity: 0.6;
}

/* 对话框头部 */
.dialog-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 16px 24px 0;
  position: relative;
  z-index: 1;
}

.dialog-title {
  font-size: 20px;
  font-weight: 600;
  color: #1e293b;
  margin: 0;
  background: linear-gradient(135deg, #6366f1, #8b5cf6);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
}

body.dark .dialog-title {
  color: #f1f5f9;
}

.close-button {
  width: 36px;
  height: 36px;
  border-radius: 18px;
  background-color: rgba(148, 163, 184, 0.1);
  border: none;
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  font-size: 20px;
  color: #64748b;
  transition: all 0.2s ease;
}

.close-button:hover {
  background-color: rgba(239, 68, 68, 0.1);
  color: #ef4444;
  transform: rotate(90deg);
}

body.dark .close-button {
  background-color: rgba(148, 163, 184, 0.1);
  color: #94a3b8;
}

body.dark .close-button:hover {
  background-color: rgba(239, 68, 68, 0.2);
  color: #f87171;
}

/* 对话框内容 */
.dialog-content {
  padding: 16px 24px;
  position: relative;
  z-index: 1;
}

/* 加载状态 */
.loading-section {
  padding: 40px 0px;
  display: flex;
  justify-content: center;
  align-items: center;
  min-height: 200px;
  text-align: center;
}

.loading-content {
  /* background: rgba(248, 250, 252, 0.8);
  border: 1px solid rgba(226, 232, 240, 0.5); */
  border-radius: 16px;
  padding: 32px;
  max-width: 400px;
  width: 100%;
  text-align: center;
  /* box-shadow: 0 4px 20px rgba(99, 102, 241, 0.1); */
  margin: 0 auto;
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 20px;
}

body.dark .loading-content {
  background: rgba(30, 41, 59, 0.8);
  border-color: rgba(71, 85, 105, 0.3);
  box-shadow: 0 4px 20px rgba(99, 102, 241, 0.2);
}

.loading-spinner {
  width: 40px;
  height: 40px;
  border: 3px solid #e2e8f0;
  border-top: 3px solid #6366f1;
  border-radius: 50%;
  animation: spin 1s linear infinite;
  flex-shrink: 0;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

.loading-details {
  width: 100%;
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 16px;
}

.loading-text {
  font-size: 16px;
  color: #64748b;
  margin: 0;
  font-weight: 500;
}

.loading-progress {
  width: 100%;
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 8px;
}

.progress-bar {
  width: 100%;
  height: 6px;
  background: rgba(226, 232, 240, 0.5);
  border-radius: 3px;
  overflow: hidden;
  position: relative;
}

.progress-fill {
  height: 100%;
  background: linear-gradient(135deg, #6366f1, #8b5cf6);
  border-radius: 3px;
  transition: width 0.1s linear;
  position: relative;
  overflow: hidden;
}

.progress-fill::before {
  content: '';
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.3), transparent);
  animation: slide 2s infinite;
}

@keyframes slide {
  0% {
    left: -100%;
  }
  100% {
    left: 100%;
  }
}

.countdown-text {
  font-size: 14px;
  color: #6366f1;
  margin: 0;
  font-weight: 600;
}

body.dark .loading-text {
  color: #94a3b8;
}

body.dark .loading-spinner {
  border-color: #374151;
  border-top-color: #818cf8;
}

body.dark .progress-bar {
  background: rgba(71, 85, 105, 0.5);
}

body.dark .countdown-text {
  color: #818cf8;
}

/* 优化结果 */
.optimization-result {
  max-height: 65vh;
  overflow-y: auto;
  padding-right: 8px;
}

.section {
  margin-bottom: 20px;
  background: rgba(248, 250, 252, 0.8);
  border-radius: 12px;
  padding: 16px;
  border: 1px solid rgba(226, 232, 240, 0.5);
}

body.dark .section {
  background: rgba(30, 41, 59, 0.5);
  border-color: rgba(71, 85, 105, 0.3);
}

.section-title {
  display: flex;
  align-items: center;
  gap: 8px;
  font-size: 16px;
  font-weight: 600;
  color: #1e293b;
  margin: 0 0 12px 0;
}

body.dark .section-title {
  color: #f1f5f9;
}

.title-icon {
  font-size: 16px;
}

/* 可编辑内容 */
.editable-content {
  background: rgba(255, 255, 255, 0.8);
  border: 1px solid #e2e8f0;
  border-radius: 6px;
  padding: 8px 12px;
  min-height: 18px;
  line-height: 1.5;
  color: #334155;
  transition: all 0.2s ease;
  cursor: text;
  font-size: 14px;
}

.editable-content:hover {
  border-color: #a5b4fc;
  background: rgba(255, 255, 255, 0.9);
}

.editable-content:focus {
  outline: none;
  border-color: #6366f1;
  box-shadow: 0 0 0 3px rgba(99, 102, 241, 0.1);
}

body.dark .editable-content {
  background: rgba(51, 65, 85, 0.5);
  border-color: #475569;
  color: #e2e8f0;
}

body.dark .editable-content:hover {
  border-color: #818cf8;
  background: rgba(51, 65, 85, 0.7);
}

body.dark .editable-content:focus {
  border-color: #818cf8;
  box-shadow: 0 0 0 3px rgba(129, 140, 248, 0.1);
}

/* 核心信息内容 */
.core-info-content > div {
  margin-bottom: 12px;
}

.core-info-content > div:last-child {
  margin-bottom: 0;
}

.core-info-content label {
  display: block;
  font-weight: 500;
  color: #475569;
  margin-bottom: 6px;
  font-size: 13px;
}

body.dark .core-info-content label {
  color: #cbd5e1;
}

/* 关键要点 */
.key-points-list {
  display: flex;
  flex-direction: column;
  gap: 12px;
}

.key-point-item {
  display: flex;
  align-items: flex-start;
  gap: 12px;
  background: rgba(255, 255, 255, 0.6);
  border-radius: 8px;
  padding: 12px;
  border: 1px solid rgba(226, 232, 240, 0.5);
}

body.dark .key-point-item {
  background: rgba(51, 65, 85, 0.3);
  border-color: rgba(71, 85, 105, 0.3);
}

.point-number {
  background: linear-gradient(135deg, #6366f1, #8b5cf6);
  color: white;
  width: 24px;
  height: 24px;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 12px;
  font-weight: 600;
  flex-shrink: 0;
  margin-top: 2px;
}

.key-point-item .editable-content {
  flex: 1;
  margin: 0;
  background: transparent;
  border: 1px dashed transparent;
  padding: 8px 12px;
}

.key-point-item .editable-content:hover {
  border-color: #a5b4fc;
  background: rgba(255, 255, 255, 0.8);
}

body.dark .key-point-item .editable-content:hover {
  background: rgba(51, 65, 85, 0.6);
}

.remove-point-btn {
  width: 24px;
  height: 24px;
  border-radius: 50%;
  background: rgba(239, 68, 68, 0.1);
  border: none;
  color: #ef4444;
  cursor: pointer;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 16px;
  transition: all 0.2s ease;
  flex-shrink: 0;
}

.remove-point-btn:hover {
  background: rgba(239, 68, 68, 0.2);
  transform: scale(1.1);
}

.add-point-btn {
  background: linear-gradient(135deg, #10b981, #059669);
  color: white;
  border: none;
  border-radius: 8px;
  padding: 10px 16px;
  font-size: 14px;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.2s ease;
  align-self: flex-start;
}

.add-point-btn:hover {
  transform: translateY(-1px);
  box-shadow: 0 4px 12px rgba(16, 185, 129, 0.3);
}

/* 风格定位内容 */
.style-positioning-content .style-item {
  margin-bottom: 12px;
}

.style-positioning-content .style-item:last-child {
  margin-bottom: 0;
}

.style-positioning-content label {
  display: block;
  font-weight: 500;
  color: #475569;
  margin-bottom: 6px;
  font-size: 13px;
}

body.dark .style-positioning-content label {
  color: #cbd5e1;
}

/* 视频规划内容 */
.video-planning-content .planning-item {
  margin-bottom: 12px;
}

.video-planning-content .planning-item:last-child {
  margin-bottom: 0;
}

.video-planning-content label {
  display: block;
  font-weight: 500;
  color: #475569;
  margin-bottom: 6px;
  font-size: 13px;
}

body.dark .video-planning-content label {
  color: #cbd5e1;
}

/* 视频结构内容 */
.video-structure-content .structure-segment {
  margin-bottom: 16px;
  background: rgba(255, 255, 255, 0.6);
  border-radius: 8px;
  padding: 14px;
  border: 1px solid rgba(226, 232, 240, 0.5);
}

body.dark .video-structure-content .structure-segment {
  background: rgba(51, 65, 85, 0.3);
  border-color: rgba(71, 85, 105, 0.3);
}

.segment-header {
  display: flex;
  flex-direction: row;
  margin-bottom: 10px;
  gap: 12px;
}

.segment-title {
  font-size: 14px;
  font-weight: 600;
  color: #1e293b;
  margin: 0;
  display: flex;
  align-items: center;
  gap: 6px;
}

.segment-title::before {
  content: '';
  width: 3px;
  height: 12px;
  background: linear-gradient(135deg, #6366f1, #8b5cf6);
  border-radius: 2px;
}

body.dark .segment-title {
  color: #f1f5f9;
}

.segment-duration {
  font-weight: 600;
  color: #6366f1;
  font-size: 12px;
  background: rgba(99, 102, 241, 0.1);
  padding: 2px 6px;
  border-radius: 3px;
}

body.dark .segment-duration {
  color: #818cf8;
  background: rgba(129, 140, 248, 0.1);
}

.structure-details .detail-item {
  margin-bottom: 10px;
}

.structure-details .detail-item:last-child {
  margin-bottom: 0;
}

.structure-details label {
  display: block;
  font-weight: 500;
  color: #475569;
  margin-bottom: 4px;
  font-size: 12px;
}

body.dark .structure-details label {
  color: #cbd5e1;
}

/* 视频片段 */
.segments-list {
  display: flex;
  flex-direction: column;
  gap: 16px;
}

.segment-item {
  background: rgba(248, 250, 252, 0.8);
  border-radius: 8px;
  padding: 16px;
  border: 1px solid rgba(226, 232, 240, 0.5);
}

body.dark .segment-item {
  background: rgba(30, 41, 59, 0.5);
  border-color: rgba(71, 85, 105, 0.3);
}


.segment-time {
  font-weight: 600;
  color: #6366f1;
  font-size: 14px;
  background: rgba(99, 102, 241, 0.1);
  padding: 4px 8px;
  border-radius: 4px;
}

body.dark .segment-time {
  color: #818cf8;
  background: rgba(129, 140, 248, 0.1);
}

.remove-segment-btn {
  width: 20px;
  height: 20px;
  border-radius: 50%;
  background: rgba(239, 68, 68, 0.1);
  border: none;
  color: #ef4444;
  cursor: pointer;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 14px;
  transition: all 0.2s ease;
}

.remove-segment-btn:hover {
  background: rgba(239, 68, 68, 0.2);
  transform: scale(1.1);
}

.segment-content .detail-item {
  margin-bottom: 8px;
}

.segment-content .detail-item:last-child {
  margin-bottom: 0;
}

.add-segment-btn {
  background: linear-gradient(135deg, #8b5cf6, #7c3aed);
  color: white;
  border: none;
  border-radius: 8px;
  padding: 10px 16px;
  font-size: 14px;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.2s ease;
  align-self: flex-start;
}

.add-segment-btn:hover {
  transform: translateY(-1px);
  box-shadow: 0 4px 12px rgba(139, 92, 246, 0.3);
}

/* 脚本内容特殊样式 */
.script-content {
  min-height: 40px;
  font-family: 'Courier New', monospace;
  background: rgba(248, 250, 252, 0.8) !important;
  border: 1px dashed #cbd5e1 !important;
  font-size: 13px;
}

body.dark .script-content {
  background: rgba(30, 41, 59, 0.6) !important;
  border-color: #64748b !important;
}

.script-content:hover {
  background: rgba(255, 255, 255, 0.9) !important;
  border-color: #a5b4fc !important;
}

body.dark .script-content:hover {
  background: rgba(30, 41, 59, 0.8) !important;
  border-color: #818cf8 !important;
}

/* 错误状态 */
.error-section {
  padding: 60px 20px;
  display: flex;
  justify-content: center;
  align-items: center;
  min-height: 200px;
  text-align: center;
}

.error-content {
  background: rgba(254, 242, 242, 0.8);
  border: 1px solid rgba(248, 113, 113, 0.2);
  border-radius: 16px;
  padding: 32px;
  max-width: 400px;
  width: 100%;
  text-align: center;
  box-shadow: 0 4px 20px rgba(239, 68, 68, 0.1);
  margin: 0 auto;
  display: flex;
  flex-direction: column;
  align-items: center;
}

body.dark .error-content {
  background: rgba(69, 26, 26, 0.8);
  border-color: rgba(248, 113, 113, 0.3);
  box-shadow: 0 4px 20px rgba(239, 68, 68, 0.2);
}

.error-icon-wrapper {
  margin-bottom: 16px;
  display: flex;
  justify-content: center;
  align-items: center;
}

.error-icon {
  font-size: 48px;
  display: inline-block;
  animation: pulse 2s infinite;
}

@keyframes pulse {
  0%, 100% {
    transform: scale(1);
    opacity: 1;
  }
  50% {
    transform: scale(1.05);
    opacity: 0.8;
  }
}

.error-details {
  width: 100%;
  display: flex;
  flex-direction: column;
  align-items: center;
}

.error-title {
  font-size: 18px;
  font-weight: 600;
  color: #dc2626;
  margin: 0 0 12px 0;
  text-align: center;
}

body.dark .error-title {
  color: #f87171;
}

.error-text {
  font-size: 14px;
  color: #7f1d1d;
  margin: 0 0 24px 0;
  line-height: 1.5;
  text-align: center;
}

body.dark .error-text {
  color: #fca5a5;
}

.error-actions {
  display: flex;
  justify-content: center;
}

.retry-btn {
  background: linear-gradient(135deg, #f59e0b, #d97706);
  color: white;
  border: none;
  border-radius: 12px;
  padding: 12px 24px;
  font-size: 14px;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.3s ease;
  display: flex;
  align-items: center;
  gap: 8px;
  box-shadow: 0 4px 12px rgba(245, 158, 11, 0.3);
}

.retry-btn:hover {
  transform: translateY(-2px);
  box-shadow: 0 6px 20px rgba(245, 158, 11, 0.4);
  background: linear-gradient(135deg, #fbbf24, #f59e0b);
}

.retry-btn:active {
  transform: translateY(0);
  box-shadow: 0 2px 8px rgba(245, 158, 11, 0.3);
}

.retry-icon {
  font-size: 16px;
  animation: rotate-slow 3s linear infinite;
}

@keyframes rotate-slow {
  from {
    transform: rotate(0deg);
  }
  to {
    transform: rotate(360deg);
  }
}

.retry-btn:hover .retry-icon {
  animation-duration: 0.5s;
}

/* 对话框底部 */
.dialog-footer {
  display: flex;
  justify-content: center;
  gap: 12px;
  padding: 0 24px 20px;
  position: relative;
  z-index: 1;
}

/* 按钮样式 */
.btn {
  padding: 10px 20px;
  border-radius: 20px;
  font-size: 14px;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.2s ease;
  border: none;
  min-width: 100px;
}

.btn:disabled {
  opacity: 0.6;
  cursor: not-allowed;
  transform: none !important;
}

.btn-secondary {
  background: #f8fafc;
  color: #64748b;
  border: 1px solid #e2e8f0;
}

.btn-secondary:hover:not(:disabled) {
  background: #f1f5f9;
  color: #475569;
  transform: translateY(-1px);
}

body.dark .btn-secondary {
  background: #374151;
  color: #9ca3af;
  border-color: #4b5563;
}

body.dark .btn-secondary:hover:not(:disabled) {
  background: #4b5563;
  color: #d1d5db;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .prompt-optimization-dialog {
    margin: 10px;
    max-width: calc(100vw - 20px);
    border-radius: 16px;
  }

  .dialog-header,
  .dialog-content,
  .dialog-footer {
    padding-left: 20px;
    padding-right: 20px;
  }

  .dialog-title {
    font-size: 20px;
  }

  .section {
    padding: 16px;
  }

  .key-point-item {
    flex-direction: column;
    gap: 8px;
  }

  .point-number {
    align-self: flex-start;
  }

  .dialog-footer {
    flex-direction: column;
  }

  .btn {
    width: 100%;
  }
}

/* 滚动条样式 */
.optimization-result::-webkit-scrollbar {
  width: 6px;
}

.optimization-result::-webkit-scrollbar-track {
  background: rgba(226, 232, 240, 0.3);
  border-radius: 3px;
}

.optimization-result::-webkit-scrollbar-thumb {
  background: rgba(148, 163, 184, 0.5);
  border-radius: 3px;
}

.optimization-result::-webkit-scrollbar-thumb:hover {
  background: rgba(148, 163, 184, 0.7);
}

body.dark .optimization-result::-webkit-scrollbar-track {
  background: rgba(71, 85, 105, 0.3);
}

body.dark .optimization-result::-webkit-scrollbar-thumb {
  background: rgba(148, 163, 184, 0.4);
}

body.dark .optimization-result::-webkit-scrollbar-thumb:hover {
  background: rgba(148, 163, 184, 0.6);
}

/* 增强可读性的样式 */
.core-info-content > div + div,
.style-positioning-content .style-item + .style-item,
.video-planning-content .planning-item + .planning-item {
  border-top: 1px solid rgba(226, 232, 240, 0.3);
  padding-top: 12px;
}

body.dark .core-info-content > div + div,
body.dark .style-positioning-content .style-item + .style-item,
body.dark .video-planning-content .planning-item + .planning-item {
  border-top-color: rgba(71, 85, 105, 0.3);
}

/* 标签样式优化 */
label {
  font-weight: 600;
  text-transform: uppercase;
  letter-spacing: 0.5px;
  color: #64748b !important;
  font-size: 11px !important;
}

body.dark label {
  color: #94a3b8 !important;
}

/* 紧凑的视频片段布局 */
.structure-segment {
  border-left: 3px solid #6366f1;
  border-radius: 0 8px 8px 0 !important;
}

.structure-segment:nth-child(even) {
  border-left-color: #8b5cf6;
}

.structure-segment:nth-child(3n) {
  border-left-color: #06b6d4;
}

/* 确保所有文本左对齐 */
.prompt-optimization-dialog * {
  text-align: left;
}

.prompt-optimization-dialog .dialog-footer {
  text-align: right;
}

.prompt-optimization-dialog h1,
.prompt-optimization-dialog h2,
.prompt-optimization-dialog h3,
.prompt-optimization-dialog h4,
.prompt-optimization-dialog h5,
.prompt-optimization-dialog h6,
.prompt-optimization-dialog p,
.prompt-optimization-dialog div,
.prompt-optimization-dialog span,
.prompt-optimization-dialog label {
  text-align: left;
}
</style>
